-- =====================================================
-- THÊM VÀ CẬP NHẬT WARD_ID, PROVINCE_ID CHO BẢNG WARD_GEOMETRY
-- =====================================================
-- Dự án: Cập nhật hệ thống hành chính VN
-- Tạo: 2025-07-15
-- Mục đích: Thêm cột ward_id và province_id vào bảng ward_geometry và fill dữ liệu
-- Database: urbox

-- =====================================================
-- PHẦN 1: KIỂM TRA DỮ LIỆU HIỆN TẠI
-- =====================================================

-- Kiểm tra cấu trúc bảng ward_geometry
SELECT 'KIỂM TRA CẤU TRÚC BẢNG WARD_GEOMETRY HIỆN TẠI' AS info;
DESCRIBE ward_geometry;

-- Kiểm tra số lượng records trong các bảng
SELECT 'KIỂM TRA SỐ LƯỢNG RECORDS' AS info;
SELECT
    'ward_geometry' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS has_ward_pti_id
FROM ward_geometry
UNION ALL
SELECT
    'ward' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) AS new_records
FROM ward
UNION ALL
SELECT
    '___province' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) AS new_records
FROM ___province;

-- =====================================================
-- PHẦN 2: THÊM CỘT WARD_ID VÀ PROVINCE_ID
-- =====================================================

SELECT 'THÊM CỘT WARD_ID VÀ PROVINCE_ID' AS info;

-- Thêm cột ward_id
ALTER TABLE ward_geometry
ADD COLUMN ward_id INT DEFAULT NULL COMMENT 'ID từ bảng ward',
ADD INDEX idx_ward_id (ward_id);

-- Thêm cột province_id
ALTER TABLE ward_geometry
ADD COLUMN province_id INT DEFAULT NULL COMMENT 'ID từ bảng ___province',
ADD INDEX idx_province_id (province_id);

-- Kiểm tra cấu trúc bảng sau khi thêm cột
SELECT 'CẤU TRÚC BẢNG SAU KHI THÊM CỘT' AS info;
DESCRIBE ward_geometry;

-- Kiểm tra mapping giữa ward_geometry và ward
SELECT 'KIỂM TRA MAPPING WARD_GEOMETRY - WARD' AS info;
SELECT 
    COUNT(wg.id) AS ward_geometry_count,
    COUNT(w.id) AS ward_matched_count,
    COUNT(wg.id) - COUNT(w.id) AS unmatched_count
FROM ward_geometry wg
LEFT JOIN ward w ON w.pti_id = wg.pti_id AND w.is_new = 2;

-- Hiển thị một số records không match (nếu có)
SELECT 'RECORDS KHÔNG MATCH (NẾU CÓ)' AS info;
SELECT wg.id, wg.pti_id, 'NO_WARD_MATCH' AS error
FROM ward_geometry wg
LEFT JOIN ward w ON w.pti_id = wg.pti_id AND w.is_new = 2
WHERE w.id IS NULL
LIMIT 10;

-- =====================================================
-- PHẦN 2: CẬP NHẬT WARD_ID
-- =====================================================

SELECT 'BẮT ĐẦU CẬP NHẬT WARD_ID' AS info;

-- Cập nhật ward_id cho bảng ward_geometry
UPDATE ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.pti_id AND w.is_new = 2
SET wg.ward_id = w.id;

-- Kiểm tra kết quả cập nhật ward_id
SELECT 'KIỂM TRA SAU KHI CẬP NHẬT WARD_ID' AS info;
SELECT 
    COUNT(*) AS total_ward_geometry,
    COUNT(CASE WHEN ward_id IS NOT NULL THEN 1 END) AS updated_ward_id,
    COUNT(CASE WHEN ward_id IS NULL THEN 1 END) AS null_ward_id
FROM ward_geometry;

-- =====================================================
-- PHẦN 3: CẬP NHẬT PROVINCE_ID
-- =====================================================

SELECT 'BẮT ĐẦU CẬP NHẬT PROVINCE_ID' AS info;

-- Cập nhật province_id cho bảng ward_geometry thông qua bảng ward
UPDATE ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.pti_id AND w.is_new = 2
INNER JOIN ___province p ON p.id = w.province_id AND p.is_new = 2
SET wg.province_id = p.id;

-- Kiểm tra kết quả cập nhật province_id
SELECT 'KIỂM TRA SAU KHI CẬP NHẬT PROVINCE_ID' AS info;
SELECT 
    COUNT(*) AS total_ward_geometry,
    COUNT(CASE WHEN province_id IS NOT NULL THEN 1 END) AS updated_province_id,
    COUNT(CASE WHEN province_id IS NULL THEN 1 END) AS null_province_id
FROM ward_geometry;

-- =====================================================
-- PHẦN 4: KIỂM TRA KẾT QUẢ CUỐI CÙNG
-- =====================================================

-- Kiểm tra tổng quan sau khi cập nhật
SELECT 'TỔNG QUAN SAU KHI CẬP NHẬT' AS info;
SELECT 
    COUNT(*) AS total_records,
    COUNT(CASE WHEN ward_id IS NOT NULL THEN 1 END) AS has_ward_id,
    COUNT(CASE WHEN province_id IS NOT NULL THEN 1 END) AS has_province_id,
    COUNT(CASE WHEN ward_id IS NOT NULL AND province_id IS NOT NULL THEN 1 END) AS complete_records
FROM ward_geometry;

-- Kiểm tra dữ liệu mẫu
SELECT 'DỮ LIỆU MẪU SAU CẬP NHẬT' AS info;
SELECT 
    wg.id,
    wg.pti_id,
    wg.ward_id,
    wg.province_id,
    w.title AS ward_title,
    p.title AS province_title
FROM ward_geometry wg
LEFT JOIN ward w ON w.id = wg.ward_id
LEFT JOIN ___province p ON p.id = wg.province_id
ORDER BY wg.province_id, wg.id
LIMIT 10;

-- Kiểm tra theo province
SELECT 'THỐNG KÊ THEO PROVINCE' AS info;
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    COUNT(wg.id) AS ward_geometry_count
FROM ___province p
LEFT JOIN ward_geometry wg ON wg.province_id = p.id
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position
LIMIT 10;

-- Kiểm tra records có vấn đề (nếu có)
SELECT 'RECORDS CÓ VẤN ĐỀ (NẾU CÓ)' AS info;
SELECT 
    wg.id,
    wg.pti_id,
    wg.ward_id,
    wg.province_id,
    CASE 
        WHEN wg.ward_id IS NULL THEN 'MISSING_WARD_ID'
        WHEN wg.province_id IS NULL THEN 'MISSING_PROVINCE_ID'
        ELSE 'OK'
    END AS status
FROM ward_geometry wg
WHERE wg.ward_id IS NULL OR wg.province_id IS NULL
LIMIT 10;

SELECT 'CẬP NHẬT HOÀN TẤT!' AS info;
