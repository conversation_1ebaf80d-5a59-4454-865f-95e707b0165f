#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector

def test_connection():
    try:
        print("🔍 Testing database connection...")
        
        # Test với các config khác nhau
        configs = [
            {
                'host': 'localhost',
                'user': 'root',
                'password': 'root',
                'database': 'urbox',
                'charset': 'utf8mb4'
            },
            {
                'host': '127.0.0.1',
                'user': 'root',
                'password': 'root',
                'database': 'urbox',
                'charset': 'utf8mb4'
            },
            {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'root',
                'database': 'urbox',
                'charset': 'utf8mb4'
            }
        ]
        
        for i, config in enumerate(configs):
            try:
                print(f"\n📝 Test config {i+1}: {config}")
                conn = mysql.connector.connect(**config)
                cursor = conn.cursor()
                
                # Test basic query
                cursor.execute("SELECT DATABASE()")
                db_name = cursor.fetchone()[0]
                print(f"✅ Connected to database: {db_name}")
                
                # Test show tables
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"📊 Found {len(tables)} tables:")
                for table in tables:
                    print(f"  - {table[0]}")
                
                # Test xaphuong table
                cursor.execute("SELECT COUNT(*) FROM xaphuong")
                count = cursor.fetchone()[0]
                print(f"📈 xaphuong table has {count} records")
                
                # Test sample data
                cursor.execute("SELECT id, tentinh, tenhc FROM xaphuong LIMIT 3")
                samples = cursor.fetchall()
                print("📋 Sample data:")
                for sample in samples:
                    print(f"  ID {sample[0]}: {sample[1]} - {sample[2]}")
                
                cursor.close()
                conn.close()
                print(f"✅ Config {i+1} works!")
                return config
                
            except Exception as e:
                print(f"❌ Config {i+1} failed: {e}")
                continue
        
        print("❌ All configs failed!")
        return None
        
    except Exception as e:
        print(f"❌ General error: {e}")
        return None

if __name__ == "__main__":
    working_config = test_connection()
    if working_config:
        print(f"\n🎉 Working config: {working_config}")
    else:
        print("\n💥 No working config found!")
