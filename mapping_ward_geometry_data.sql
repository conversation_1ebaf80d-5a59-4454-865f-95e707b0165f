-- =====================================================
-- MAPPING DỮ LIỆU GIỮA WARD_GEOMETRY, WARD VÀ ___PROVINCE
-- =====================================================
-- Dự án: Cập nhật hệ thống hành chính VN
-- Tạo: 2025-07-15
-- Mục đích: Mapping ward_geometry.ward_pti_id với ward.pti_id và ward.province_id với ___province.id
-- Database: urbox

-- =====================================================
-- PHẦN 1: KIỂM TRA CẤU TRÚC VÀ DỮ LIỆU HIỆN TẠI
-- =====================================================

-- Kiểm tra cấu trúc các bảng
SELECT 'CẤU TRÚC BẢNG WARD_GEOMETRY' AS info;
DESCRIBE ward_geometry;

SELECT 'CẤU TRÚC BẢNG WARD' AS info;
DESCRIBE ward;

SELECT 'CẤU TRÚC BẢNG ___PROVINCE' AS info;
DESCRIBE ___province;

-- Kiểm tra số lượng records trong các bảng
SELECT 'SỐ LƯỢNG RECORDS TRONG CÁC BẢNG' AS info;
SELECT 
    'ward_geometry' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS has_ward_pti_id
FROM ward_geometry
UNION ALL
SELECT 
    'ward' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) AS new_records
FROM ward
UNION ALL
SELECT 
    '___province' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) AS new_records
FROM ___province;

-- =====================================================
-- PHẦN 2: KIỂM TRA MAPPING WARD_GEOMETRY - WARD
-- =====================================================

SELECT 'KIỂM TRA MAPPING WARD_GEOMETRY.WARD_PTI_ID - WARD.PTI_ID' AS info;

-- Kiểm tra mapping giữa ward_geometry và ward
SELECT 
    COUNT(wg.id) AS ward_geometry_total,
    COUNT(w.id) AS ward_matched,
    COUNT(wg.id) - COUNT(w.id) AS unmatched_count
FROM ward_geometry wg
LEFT JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2;

-- Hiển thị records không match (nếu có)
SELECT 'WARD_GEOMETRY RECORDS KHÔNG MATCH VỚI WARD' AS info;
SELECT 
    wg.id AS ward_geometry_id,
    wg.ward_pti_id,
    wg.ward_title,
    'NO_WARD_MATCH' AS status
FROM ward_geometry wg
LEFT JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2
WHERE w.id IS NULL
LIMIT 10;

-- Hiển thị một số records match thành công
SELECT 'MỘT SỐ RECORDS MATCH THÀNH CÔNG' AS info;
SELECT 
    wg.id AS ward_geometry_id,
    wg.ward_pti_id,
    wg.ward_title AS ward_geometry_title,
    w.id AS ward_id,
    w.pti_id AS ward_pti_id,
    w.title AS ward_title,
    w.province_id
FROM ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2
ORDER BY w.province_id, wg.id
LIMIT 10;

-- =====================================================
-- PHẦN 3: KIỂM TRA MAPPING WARD - ___PROVINCE
-- =====================================================

SELECT 'KIỂM TRA MAPPING WARD.PROVINCE_ID - ___PROVINCE.ID' AS info;

-- Kiểm tra mapping giữa ward và ___province
SELECT 
    COUNT(w.id) AS ward_total,
    COUNT(p.id) AS province_matched,
    COUNT(w.id) - COUNT(p.id) AS unmatched_count
FROM ward w
LEFT JOIN ___province p ON p.id = w.province_id AND p.is_new = 2
WHERE w.is_new = 2;

-- Hiển thị ward records không match với province (nếu có)
SELECT 'WARD RECORDS KHÔNG MATCH VỚI PROVINCE' AS info;
SELECT 
    w.id AS ward_id,
    w.pti_id AS ward_pti_id,
    w.province_id,
    w.title AS ward_title,
    'NO_PROVINCE_MATCH' AS status
FROM ward w
LEFT JOIN ___province p ON p.id = w.province_id AND p.is_new = 2
WHERE w.is_new = 2 AND p.id IS NULL
LIMIT 10;

-- =====================================================
-- PHẦN 4: MAPPING HOÀN CHỈNH 3 BẢNG
-- =====================================================

SELECT 'MAPPING HOÀN CHỈNH: WARD_GEOMETRY - WARD - ___PROVINCE' AS info;

-- Hiển thị mapping hoàn chỉnh giữa 3 bảng
SELECT 
    wg.id AS ward_geometry_id,
    wg.ward_pti_id,
    wg.ward_title AS geometry_ward_title,
    w.id AS ward_id,
    w.title AS ward_title,
    w.province_id,
    p.id AS province_id_check,
    p.title AS province_title,
    p.pti_id AS province_pti_id
FROM ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2
INNER JOIN ___province p ON p.id = w.province_id AND p.is_new = 2
ORDER BY p.position, w.id
LIMIT 20;

-- Thống kê theo province
SELECT 'THỐNG KÊ WARD_GEOMETRY THEO PROVINCE' AS info;
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    p.pti_id AS province_pti_id,
    COUNT(wg.id) AS ward_geometry_count,
    COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_new = 2
LEFT JOIN ward_geometry wg ON wg.ward_pti_id = w.pti_id
WHERE p.is_new = 2
GROUP BY p.id, p.title, p.pti_id
ORDER BY p.position
LIMIT 15;

-- =====================================================
-- PHẦN 5: KIỂM TRA TỔNG QUAN
-- =====================================================

SELECT 'TỔNG QUAN MAPPING' AS info;
SELECT 
    'Total ward_geometry records' AS description,
    COUNT(*) AS count
FROM ward_geometry
UNION ALL
SELECT 
    'Ward_geometry có mapping với ward',
    COUNT(*)
FROM ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2
UNION ALL
SELECT 
    'Ward_geometry có mapping đầy đủ với ward và province',
    COUNT(*)
FROM ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_new = 2
INNER JOIN ___province p ON p.id = w.province_id AND p.is_new = 2;

SELECT 'MAPPING HOÀN TẤT!' AS info;
