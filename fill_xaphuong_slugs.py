#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để fill dữ liệu province_slug và ward_slug cho bảng xaphuong
- province_slug: từ tentinh, bỏ prefix như "tỉnh", "thành phố", "thủ đô"
- ward_slug: từ tenhc
"""

import mysql.connector
import re
import time

def remove_vietnamese_accents(text):
    """Bỏ dấu tiếng Việt"""
    vietnamese_map = {
        'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
        'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
        'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
        'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
        'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd',
        'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
        'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
        'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
        'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
        'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
        'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
        'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
        'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
        'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
        'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
        'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
        'Ỳ': 'Y', 'Ý': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y', 'Ỵ': 'Y',
        'Đ': 'D'
    }
    
    result = ''
    for char in text:
        result += vietnamese_map.get(char, char)
    
    return result

def create_province_slug(tentinh):
    """Tạo province_slug từ tentinh, bỏ prefix"""
    if not tentinh:
        return ''
    
    # Bỏ các prefix
    clean_title = tentinh
    prefixes = ['Thủ đô ', 'Thành phố ', 'Tỉnh ', 'tỉnh ', 'thành phố ', 'thủ đô ']
    
    for prefix in prefixes:
        if clean_title.startswith(prefix):
            clean_title = clean_title[len(prefix):]
            break
    
    # Chuyển thành chữ thường
    clean_title = clean_title.lower()
    
    # Bỏ dấu tiếng Việt
    clean_title = remove_vietnamese_accents(clean_title)
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    
    # Bỏ dấu gạch ngang ở đầu và cuối
    clean_title = clean_title.strip('-')
    
    return clean_title

def create_ward_slug(tenhc):
    """Tạo ward_slug từ tenhc"""
    if not tenhc:
        return ''
    
    # Chuyển thành chữ thường
    clean_title = tenhc.lower()
    
    # Bỏ dấu tiếng Việt
    clean_title = remove_vietnamese_accents(clean_title)
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    
    # Bỏ dấu gạch ngang ở đầu và cuối
    clean_title = clean_title.strip('-')
    
    return clean_title

def main():
    """Main function"""
    print("🚀 Bắt đầu fill dữ liệu province_slug và ward_slug cho bảng xaphuong...")
    
    # Kết nối database
    try:
        conn = mysql.connector.connect(
            host='127.0.0.1',
            user='root',
            password='root',
            database='urbox',
            charset='utf8mb4',
            autocommit=False
        )
        cursor = conn.cursor()
        print("✅ Kết nối database thành công!")

        # Test connection
        cursor.execute("SELECT COUNT(*) FROM xaphuong")
        count = cursor.fetchone()[0]
        print(f"📊 Tìm thấy {count} records trong bảng xaphuong")

    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return
    
    try:
        # Lấy dữ liệu từ bảng xaphuong
        print("📊 Lấy dữ liệu từ bảng xaphuong...")
        cursor.execute("SELECT id, tentinh, tenhc FROM xaphuong ORDER BY id")
        records = cursor.fetchall()
        
        print(f"📝 Tìm thấy {len(records)} records trong bảng xaphuong")
        
        # Xử lý từng record
        updated_count = 0
        error_count = 0
        
        for record in records:
            record_id, tentinh, tenhc = record
            
            try:
                # Tạo slug
                province_slug = create_province_slug(tentinh)
                ward_slug = create_ward_slug(tenhc)
                
                # Update database
                update_sql = """
                UPDATE xaphuong 
                SET province_slug = %s, ward_slug = %s 
                WHERE id = %s
                """
                
                cursor.execute(update_sql, (province_slug, ward_slug, record_id))
                updated_count += 1
                
                if updated_count % 100 == 0:
                    print(f"📝 Đã update {updated_count} records...")
                    conn.commit()  # Commit mỗi 100 records
                
            except Exception as e:
                print(f"❌ Lỗi update record ID {record_id}: {e}")
                error_count += 1
                continue
        
        # Commit cuối cùng
        conn.commit()
        
        print(f"\n🎉 Hoàn thành!")
        print(f"✅ Đã update thành công: {updated_count} records")
        print(f"❌ Lỗi: {error_count} records")
        
        # Hiển thị một vài ví dụ
        print("\n📋 Một vài ví dụ kết quả:")
        cursor.execute("""
        SELECT id, tentinh, tenhc, province_slug, ward_slug 
        FROM xaphuong 
        WHERE province_slug IS NOT NULL AND ward_slug IS NOT NULL
        ORDER BY id 
        LIMIT 10
        """)
        
        examples = cursor.fetchall()
        for example in examples:
            record_id, tentinh, tenhc, province_slug, ward_slug = example
            print(f"ID {record_id}: '{tentinh}' -> '{province_slug}' | '{tenhc}' -> '{ward_slug}'")
        
    except Exception as e:
        print(f"❌ Lỗi xử lý: {e}")
        conn.rollback()
    
    finally:
        cursor.close()
        conn.close()
        print("🔒 Đã đóng kết nối database")

if __name__ == "__main__":
    main()
