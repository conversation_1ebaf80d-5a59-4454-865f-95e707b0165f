-- =====================================================
-- FILL DỮ LIỆU MAPPING CHO BẢNG WARD_GEOMETRY
-- =====================================================
-- Dự án: Cập nhật hệ thống hành chính VN
-- Tạo: 2025-07-15
-- Mục đích: Fill ward_pti_id, province_pti_id, ward_title, province_title vào bảng ward_geometry
-- Database: urbox

-- =====================================================
-- PHẦN 1: KIỂM TRA CẤU TRÚC VÀ DỮ LIỆU HIỆN TẠI
-- =====================================================

SELECT 'BẮT ĐẦU QUAY TRÌNH FILL DỮ LIỆU MAPPING CHO WARD_GEOMETRY' AS info;
SELECT NOW() AS start_time;

-- Kiểm tra cấu trúc bảng ward_geometry hiện tại
SELECT 'KIỂM TRA CẤU TRÚC BẢNG WARD_GEOMETRY' AS info;
DESCRIBE ward_geometry;

-- Kiểm tra số lượng records trong các bảng
SELECT 'KIỂM TRA SỐ LƯỢNG RECORDS TRONG CÁC BẢNG' AS info;
SELECT 
    'ward_geometry' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN pti_id IS NOT NULL THEN 1 END) AS has_pti_id,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS has_ward_pti_id,
    COUNT(CASE WHEN province_pti_id IS NOT NULL THEN 1 END) AS has_province_pti_id
FROM ward_geometry
UNION ALL
SELECT 
    'xaphuong' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN maxa IS NOT NULL THEN 1 END) AS has_maxa,
    COUNT(CASE WHEN ma IS NOT NULL THEN 1 END) AS has_ma,
    COUNT(CASE WHEN matinh IS NOT NULL THEN 1 END) AS has_matinh
FROM xaphuong
UNION ALL
SELECT 
    'ward' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_merge = 2 THEN 1 END) AS new_records,
    0 AS extra_col1,
    0 AS extra_col2
FROM ward
UNION ALL
SELECT 
    '___province' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_merge = 2 THEN 1 END) AS new_records,
    0 AS extra_col1,
    0 AS extra_col2
FROM ___province;

-- =====================================================
-- PHẦN 2: THÊM CÁC CỘT CẦN THIẾT (NẾU CHƯA CÓ)
-- =====================================================

SELECT 'THÊM CÁC CỘT CẦN THIẾT VÀO BẢNG WARD_GEOMETRY' AS info;

-- Thêm cột ward_pti_id (nếu chưa có)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'urbox' AND TABLE_NAME = 'ward_geometry' AND COLUMN_NAME = 'ward_pti_id') > 0,
    'SELECT "Cột ward_pti_id đã tồn tại" AS info',
    'ALTER TABLE ward_geometry ADD COLUMN ward_pti_id INT DEFAULT NULL COMMENT "PTI ID từ xaphuong.ma", ADD INDEX idx_ward_pti_id (ward_pti_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thêm cột province_pti_id (nếu chưa có)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'urbox' AND TABLE_NAME = 'ward_geometry' AND COLUMN_NAME = 'province_pti_id') > 0,
    'SELECT "Cột province_pti_id đã tồn tại" AS info',
    'ALTER TABLE ward_geometry ADD COLUMN province_pti_id INT DEFAULT NULL COMMENT "PTI ID từ xaphuong.matinh", ADD INDEX idx_province_pti_id (province_pti_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thêm cột ward_title (nếu chưa có)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'urbox' AND TABLE_NAME = 'ward_geometry' AND COLUMN_NAME = 'ward_title') > 0,
    'SELECT "Cột ward_title đã tồn tại" AS info',
    'ALTER TABLE ward_geometry ADD COLUMN ward_title VARCHAR(255) DEFAULT NULL COMMENT "Tên xã phường từ bảng ward"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thêm cột province_title (nếu chưa có)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'urbox' AND TABLE_NAME = 'ward_geometry' AND COLUMN_NAME = 'province_title') > 0,
    'SELECT "Cột province_title đã tồn tại" AS info',
    'ALTER TABLE ward_geometry ADD COLUMN province_title VARCHAR(255) DEFAULT NULL COMMENT "Tên tỉnh thành từ bảng ___province"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Kiểm tra cấu trúc bảng sau khi thêm cột
SELECT 'CẤU TRÚC BẢNG SAU KHI THÊM CÁC CỘT' AS info;
DESCRIBE ward_geometry;

-- =====================================================
-- PHẦN 3: KIỂM TRA MAPPING GIỮA CÁC BẢNG
-- =====================================================

SELECT 'KIỂM TRA MAPPING GIỮA WARD_GEOMETRY VÀ XAPHUONG' AS info;

-- Kiểm tra mapping ward_geometry.pti_id với xaphuong.maxa
SELECT 
    COUNT(wg.id) AS ward_geometry_total,
    COUNT(x.id) AS xaphuong_matched,
    COUNT(wg.id) - COUNT(x.id) AS unmatched_count
FROM ward_geometry wg
LEFT JOIN xaphuong x ON x.maxa = wg.pti_id;

-- Hiển thị một số records không match (nếu có)
SELECT 'WARD_GEOMETRY RECORDS KHÔNG MATCH VỚI XAPHUONG (NẾU CÓ)' AS info;
SELECT 
    wg.id AS ward_geometry_id,
    wg.pti_id AS ward_geometry_pti_id,
    'NO_XAPHUONG_MATCH' AS status
FROM ward_geometry wg
LEFT JOIN xaphuong x ON x.maxa = wg.pti_id
WHERE x.id IS NULL
LIMIT 5;

-- =====================================================
-- PHẦN 4: FILL DỮ LIỆU WARD_PTI_ID VÀ PROVINCE_PTI_ID
-- =====================================================

SELECT 'BẮT ĐẦU FILL WARD_PTI_ID VÀ PROVINCE_PTI_ID' AS info;
SELECT NOW() AS step_start_time;

-- Cập nhật ward_pti_id và province_pti_id từ bảng xaphuong
UPDATE ward_geometry wg
INNER JOIN xaphuong x ON x.maxa = wg.pti_id
SET 
    wg.ward_pti_id = x.ma,
    wg.province_pti_id = x.matinh;

-- Kiểm tra kết quả sau khi cập nhật
SELECT 'KẾT QUẢ SAU KHI FILL WARD_PTI_ID VÀ PROVINCE_PTI_ID' AS info;
SELECT 
    COUNT(*) AS total_ward_geometry,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS updated_ward_pti_id,
    COUNT(CASE WHEN province_pti_id IS NOT NULL THEN 1 END) AS updated_province_pti_id,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL AND province_pti_id IS NOT NULL THEN 1 END) AS both_updated
FROM ward_geometry;

-- =====================================================
-- PHẦN 5: FILL DỮ LIỆU WARD_TITLE
-- =====================================================

SELECT 'BẮT ĐẦU FILL WARD_TITLE' AS info;
SELECT NOW() AS step_start_time;

-- Cập nhật ward_title từ bảng ward
UPDATE ward_geometry wg
INNER JOIN ward w ON w.pti_id = wg.ward_pti_id AND w.is_merge = 2
SET wg.ward_title = w.title;

-- Kiểm tra kết quả sau khi cập nhật ward_title
SELECT 'KẾT QUẢ SAU KHI FILL WARD_TITLE' AS info;
SELECT 
    COUNT(*) AS total_ward_geometry,
    COUNT(CASE WHEN ward_title IS NOT NULL THEN 1 END) AS updated_ward_title,
    COUNT(CASE WHEN ward_title IS NULL THEN 1 END) AS null_ward_title
FROM ward_geometry;

-- =====================================================
-- PHẦN 6: FILL DỮ LIỆU PROVINCE_TITLE
-- =====================================================

SELECT 'BẮT ĐẦU FILL PROVINCE_TITLE' AS info;
SELECT NOW() AS step_start_time;

-- Cập nhật province_title từ bảng ___province
UPDATE ward_geometry wg
INNER JOIN ___province p ON p.pti_id = wg.province_pti_id AND p.is_merge = 2
SET wg.province_title = p.title;

-- Kiểm tra kết quả sau khi cập nhật province_title
SELECT 'KẾT QUẢ SAU KHI FILL PROVINCE_TITLE' AS info;
SELECT 
    COUNT(*) AS total_ward_geometry,
    COUNT(CASE WHEN province_title IS NOT NULL THEN 1 END) AS updated_province_title,
    COUNT(CASE WHEN province_title IS NULL THEN 1 END) AS null_province_title
FROM ward_geometry;

-- =====================================================
-- PHẦN 7: KIỂM TRA KẾT QUẢ CUỐI CÙNG
-- =====================================================

SELECT 'TỔNG QUAN KẾT QUẢ CUỐI CÙNG' AS info;
SELECT NOW() AS final_check_time;

-- Thống kê tổng quan
SELECT 
    COUNT(*) AS total_records,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS has_ward_pti_id,
    COUNT(CASE WHEN province_pti_id IS NOT NULL THEN 1 END) AS has_province_pti_id,
    COUNT(CASE WHEN ward_title IS NOT NULL THEN 1 END) AS has_ward_title,
    COUNT(CASE WHEN province_title IS NOT NULL THEN 1 END) AS has_province_title,
    COUNT(CASE WHEN ward_pti_id IS NOT NULL AND province_pti_id IS NOT NULL 
               AND ward_title IS NOT NULL AND province_title IS NOT NULL THEN 1 END) AS complete_records,
    ROUND(COUNT(CASE WHEN ward_pti_id IS NOT NULL AND province_pti_id IS NOT NULL 
                     AND ward_title IS NOT NULL AND province_title IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) AS success_rate
FROM ward_geometry;

-- Hiển thị dữ liệu mẫu
SELECT 'DỮ LIỆU MẪU SAU KHI HOÀN THÀNH' AS info;
SELECT 
    wg.id,
    wg.pti_id AS original_pti_id,
    wg.ward_pti_id,
    wg.province_pti_id,
    wg.ward_title,
    wg.province_title
FROM ward_geometry wg
WHERE wg.ward_pti_id IS NOT NULL AND wg.province_pti_id IS NOT NULL
ORDER BY wg.province_pti_id, wg.id
LIMIT 10;

-- Thống kê theo province
SELECT 'THỐNG KÊ THEO PROVINCE' AS info;
SELECT 
    wg.province_pti_id,
    wg.province_title,
    COUNT(wg.id) AS ward_geometry_count
FROM ward_geometry wg
WHERE wg.province_pti_id IS NOT NULL
GROUP BY wg.province_pti_id, wg.province_title
ORDER BY wg.province_pti_id
LIMIT 15;

-- Kiểm tra records có vấn đề (nếu có)
SELECT 'RECORDS CÓ VẤN ĐỀ (NẾU CÓ)' AS info;
SELECT 
    wg.id,
    wg.pti_id,
    wg.ward_pti_id,
    wg.province_pti_id,
    wg.ward_title,
    wg.province_title,
    CASE 
        WHEN wg.ward_pti_id IS NULL THEN 'MISSING_WARD_PTI_ID'
        WHEN wg.province_pti_id IS NULL THEN 'MISSING_PROVINCE_PTI_ID'
        WHEN wg.ward_title IS NULL THEN 'MISSING_WARD_TITLE'
        WHEN wg.province_title IS NULL THEN 'MISSING_PROVINCE_TITLE'
        ELSE 'OK'
    END AS status
FROM ward_geometry wg
WHERE wg.ward_pti_id IS NULL OR wg.province_pti_id IS NULL 
   OR wg.ward_title IS NULL OR wg.province_title IS NULL
LIMIT 10;

SELECT 'HOÀN THÀNH FILL DỮ LIỆU MAPPING CHO WARD_GEOMETRY!' AS info;
SELECT NOW() AS end_time;
