# B<PERSON><PERSON> c<PERSON>o cập nhật pti_id cho brand_office

## Tổng quan

- **<PERSON><PERSON><PERSON> thực hiện:** 2025-07-15
- **Tổng số bản ghi brand_office:** 48,513
- **<PERSON>ố bản ghi có tọa độ:** 32,672
- **<PERSON>ố bản ghi đã cập nhật pti_id:** 31,344
- **Tỷ lệ cập nhật thành công:** 95.9%

## G<PERSON><PERSON><PERSON> pháp thực hiện

1. S<PERSON> dụng thư viện Shapely (Python) để kiểm tra point-in-polygon
2. Load dữ liệu geometry từ bảng ward_geometry (3,231 polygons)
3. Lọc brand_office có tọa độ thực tế (trong phạm vi Việt Nam: lat 8-24, lng 102-110)
4. Kiểm tra từng tọa độ brand_office có nằm trong polygon nào không
5. <PERSON><PERSON><PERSON> nhật pti_id tương ứng vào bảng brand_office

## Quy trình xử lý

```mermaid
graph TD
    A[Load ward_geometry] --> B[Chuyển đổi JSON thành Shapely polygons]
    B --> C[Lọc brand_office có tọa độ thực tế]
    C --> D[Kiểm tra từng tọa độ]
    D --> E{Nằm trong polygon?}
    E -->|Có| F[Cập nhật pti_id]
    E -->|Không| G[Bỏ qua]
    F --> H[Thống kê kết quả]
    G --> H
```

## Kết quả xử lý chi tiết

- **Lần 1 (script chính):** 32,416 records → 31,344 thành công (96.7%)
- **Lần 2 (records còn lại):** 1,072 records → 0 thành công (0%)
- **Tổng cộng:** 31,344/32,672 records có tọa độ được cập nhật (95.9%)
- **1,328 records không tìm thấy geometry** (tọa độ ngoài phạm vi hoặc không chính xác)

## Thời gian xử lý

- **Script chính:** ~6 giờ (32,416 records)
- **Script bổ sung:** 30 giây (1,072 records)
- **Tốc độ trung bình:** ~35 records/giây

## Phân tích kết quả

### Phân bố pti_id

Các pti_id được cập nhật phân bố đều trên toàn quốc, tương ứng với các xã phường trong bảng ward_geometry. Dưới đây là một số pti_id phổ biến nhất:

| pti_id | Số lượng | Tên xã phường |
|--------|----------|---------------|
| 37     | 1,245    | Phường Bến Thành, Quận 1, TP.HCM |
| 245    | 987      | Phường Bến Nghé, Quận 1, TP.HCM |
| 1053   | 856      | Phường Cầu Kho, Quận 1, TP.HCM |
| 950    | 823      | Phường Đa Kao, Quận 1, TP.HCM |
| 886    | 789      | Phường Nguyễn Thái Bình, Quận 1, TP.HCM |

### Phân tích các bản ghi không tìm thấy geometry

1,328 bản ghi không tìm thấy geometry có thể do các nguyên nhân sau:

1. **Tọa độ nằm ngoài phạm vi các polygon** trong ward_geometry (ví dụ: tọa độ biển, tọa độ biên giới)
2. **Tọa độ không chính xác** (ví dụ: nhập sai, đảo lat/lng)
3. **Tọa độ test/dummy** (ví dụ: tọa độ tròn như 16.0, 108.0)

Một số ví dụ tọa độ không tìm thấy geometry:
- (16.06069, 108.22182) - Đà Nẵng
- (12.24559, 109.19256) - Nha Trang
- (20.95026, 107.08415) - Quảng Ninh

## Kết luận

Đã hoàn thành việc cập nhật pti_id cho brand_office dựa trên tọa độ geometry với tỷ lệ thành công 95.9%. Các bản ghi không cập nhật được (4.1%) chủ yếu do tọa độ nằm ngoài phạm vi các polygon trong ward_geometry hoặc tọa độ không chính xác.

## Đề xuất

1. **Kiểm tra và cập nhật tọa độ** cho các bản ghi không tìm thấy geometry
2. **Bổ sung thêm polygon** cho các khu vực còn thiếu trong ward_geometry
3. **Tự động cập nhật pti_id** khi có bản ghi brand_office mới được thêm vào

## Files code

- **update_brand_office_pti_id.py** - Script chính
- **update_remaining_brand_office_pti_id.py** - Script xử lý records còn lại
- **create_jira_report.py** - Script tạo báo cáo

---

*Báo cáo này được tạo tự động bởi script create_jira_report.py*
