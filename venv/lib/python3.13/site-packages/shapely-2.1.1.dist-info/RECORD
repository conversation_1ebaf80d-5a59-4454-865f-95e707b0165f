shapely-2.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
shapely-2.1.1.dist-info/METADATA,sha256=NGZ3Ir07AJWBfVBqUaQSo73L3V_FOCyoJFmTwMMfO9k,6765
shapely-2.1.1.dist-info/RECORD,,
shapely-2.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely-2.1.1.dist-info/WHEEL,sha256=VIOxLMMkshvS_PbEukmsYu1sg_oxjW5SBJ1JnbuDdDk,136
shapely-2.1.1.dist-info/licenses/LICENSE.txt,sha256=SiB-rJvyV-0obhxvD84lPRq_2xNPxh7IVeN_VpKj91Q,1583
shapely-2.1.1.dist-info/licenses/LICENSE_GEOS,sha256=E4ssL5RQBz18EWq-OXykOCYlZxx1K3qaXzILM_w_Pyo,26786
shapely-2.1.1.dist-info/top_level.txt,sha256=fxc5UIKKldpKP3lx2dvM5R3hWeKwlmVW6-nfikr3iU0,8
shapely/.dylibs/libgeos.3.13.1.dylib,sha256=AF7IJzAindOBjUCGWqnO0RI8yNidK-J4_3e5PfWYImY,3258528
shapely/.dylibs/libgeos_c.1.19.2.dylib,sha256=KddM9Ll0fuWBttNcUTKBZy-p6fza-YlrU1el5Xc_vXw,352048
shapely/__init__.py,sha256=7EaUQ1fhIHeYITv5jnFc-GKQbsMXCCSU-YI_I3D9cOM,1031
shapely/__pycache__/__init__.cpython-313.pyc,,
shapely/__pycache__/_coverage.cpython-313.pyc,,
shapely/__pycache__/_enum.cpython-313.pyc,,
shapely/__pycache__/_geometry.cpython-313.pyc,,
shapely/__pycache__/_ragged_array.cpython-313.pyc,,
shapely/__pycache__/_version.cpython-313.pyc,,
shapely/__pycache__/affinity.cpython-313.pyc,,
shapely/__pycache__/conftest.cpython-313.pyc,,
shapely/__pycache__/constructive.cpython-313.pyc,,
shapely/__pycache__/coordinates.cpython-313.pyc,,
shapely/__pycache__/coords.cpython-313.pyc,,
shapely/__pycache__/creation.cpython-313.pyc,,
shapely/__pycache__/decorators.cpython-313.pyc,,
shapely/__pycache__/errors.cpython-313.pyc,,
shapely/__pycache__/geos.cpython-313.pyc,,
shapely/__pycache__/io.cpython-313.pyc,,
shapely/__pycache__/linear.cpython-313.pyc,,
shapely/__pycache__/measurement.cpython-313.pyc,,
shapely/__pycache__/ops.cpython-313.pyc,,
shapely/__pycache__/plotting.cpython-313.pyc,,
shapely/__pycache__/predicates.cpython-313.pyc,,
shapely/__pycache__/prepared.cpython-313.pyc,,
shapely/__pycache__/set_operations.cpython-313.pyc,,
shapely/__pycache__/speedups.cpython-313.pyc,,
shapely/__pycache__/strtree.cpython-313.pyc,,
shapely/__pycache__/testing.cpython-313.pyc,,
shapely/__pycache__/validation.cpython-313.pyc,,
shapely/__pycache__/wkb.cpython-313.pyc,,
shapely/__pycache__/wkt.cpython-313.pyc,,
shapely/_coverage.py,sha256=yiCL5ZtMKMDHVw6ElHkR8C_C2n0DtRpsgsGJhhuucL4,5732
shapely/_enum.py,sha256=yjnEScziJZJLaM0P1rYGPekN_-mWd64-rme5PsaKeHk,704
shapely/_geometry.py,sha256=XZxvmLB_jg0lHoMiHgKFd_LNSij4nrJ_QYNKpq-hboQ,29155
shapely/_geometry_helpers.cpython-313-darwin.so,sha256=tIRP6J9z1pv0y2i4D8f7hs68uyec0TwchURnyP7OhAs,502224
shapely/_geos.cpython-313-darwin.so,sha256=LjFPuptVZFQJ4w--ecFNr3mSP1LTNzRSuakDjbiOL5c,154016
shapely/_geos.pxd,sha256=GPVuqpe1YIaQVbMRJoZIiCjxdme0cJAg_z4DpcE7vDc,3209
shapely/_pygeos_api.pxd,sha256=Eq4ZaI0ufjGXA_TGkpHS5sOOaT1is4w3XaQ4swq9KH8,2090
shapely/_ragged_array.py,sha256=-eaMAVJtZv3P0dzGdcZwx8qJEt49EFmS384VAUlTAQE,17445
shapely/_version.py,sha256=oV3lip39MZVjukx59WJWuuHAB0PVNQDlWQf1kVRjIWk,497
shapely/affinity.py,sha256=7hWT9l_MiEkj9VECKukdAUz_NYSoh3vI6hvNV9A6PYo,8255
shapely/algorithms/__init__.py,sha256=ZHIivuQ5w5GJXB7hwLp0zckd_b226k68wjBhT0k9Ayo,41
shapely/algorithms/__pycache__/__init__.cpython-313.pyc,,
shapely/algorithms/__pycache__/_oriented_envelope.cpython-313.pyc,,
shapely/algorithms/__pycache__/cga.cpython-313.pyc,,
shapely/algorithms/__pycache__/polylabel.cpython-313.pyc,,
shapely/algorithms/_oriented_envelope.py,sha256=aXoxegHXQKzESqo8KU0qXyvYLh8ALJjWlGt0GORS8vQ,1933
shapely/algorithms/cga.py,sha256=KNXvGaIT5JxH2k_6wnLBknNdROT3coD2fYIup-YP76I,1757
shapely/algorithms/polylabel.py,sha256=gzV5lcbzZlvl_wUNCWQFa78m3hPyuZCuBMlA_4HuaWQ,1344
shapely/conftest.py,sha256=B-sFq61da65kTXfRgMf-WjPcZZ_put27XAdbqfK0fGo,1947
shapely/constructive.py,sha256=nIFNT7D25eOiqag4Gno_8el8lVaZXuJcX0N5E-Gv0XM,56189
shapely/coordinates.py,sha256=tvEeJIM1GMnwYZNEKh-3m51cHgJabZYaASShDhE8-xM,12575
shapely/coords.py,sha256=IbuodmCNibkZc8_Xpa8e7HFxT4W-oCMY4ftvJTuDGXk,3032
shapely/creation.py,sha256=wtp6USiIY6wp4Jp96xsy2ae7a9KgK9S5nFb0PbZEs20,30987
shapely/decorators.py,sha256=EwVrR1b54NjNM8LRW0xfaueLsHit1LQCnJIxNxkn8ck,5694
shapely/errors.py,sha256=MNWjK339jfpZ59UmVZgVsX2tyZCw3nRCZa0j_zI8zlw,2398
shapely/geometry/__init__.py,sha256=-YrRxKZ-HfT3yVG4klD5EEvdFHfwWNAK6ovjuuNhhlo,763
shapely/geometry/__pycache__/__init__.cpython-313.pyc,,
shapely/geometry/__pycache__/base.cpython-313.pyc,,
shapely/geometry/__pycache__/collection.cpython-313.pyc,,
shapely/geometry/__pycache__/geo.cpython-313.pyc,,
shapely/geometry/__pycache__/linestring.cpython-313.pyc,,
shapely/geometry/__pycache__/multilinestring.cpython-313.pyc,,
shapely/geometry/__pycache__/multipoint.cpython-313.pyc,,
shapely/geometry/__pycache__/multipolygon.cpython-313.pyc,,
shapely/geometry/__pycache__/point.cpython-313.pyc,,
shapely/geometry/__pycache__/polygon.cpython-313.pyc,,
shapely/geometry/base.py,sha256=ew2WuCfPl-iOrJsr34Nlm6hOmdAOlxgp-nHIzBQ3YSI,40375
shapely/geometry/collection.py,sha256=xEjq5ArbA5e-e3x6pbc6XDQ7YyiBiS-5JbneytDEM3Q,1736
shapely/geometry/geo.py,sha256=U3civBiWmrozAApjhiqpXYBPi3YSjwrAJqzsMFCiPTc,4366
shapely/geometry/linestring.py,sha256=YG_xDpCeGrizQqEObKj8D4U6yVyfvq_Ybh8vZIGfQXI,7599
shapely/geometry/multilinestring.py,sha256=tRKc6crL6mWtrUp0PRIdKZJpibwVOKkF9042K1rjEl4,2942
shapely/geometry/multipoint.py,sha256=dGyKGxMOuvIM1DAbzxB9X3XSjDvYpSUNtYkUgFY1p_g,3222
shapely/geometry/multipolygon.py,sha256=BTkOVpK8zE6P64EzeKv3NmAcy6-ol-ViCR0jnPhCbY4,3955
shapely/geometry/point.py,sha256=pIB8Vc_6N7VNnnbguqjHwOA9uS49BbSE2UwOMC58XbE,4731
shapely/geometry/polygon.py,sha256=********************************-jJJICM8Ucg,11082
shapely/geos.py,sha256=CAPZSwI3su1ktoQG4iqtWfDGLZTIL7YgZ416LtlqoCQ,520
shapely/io.py,sha256=Ad71zu0LwctyD7RatVcwZFsvOQ_VN-bLykJAOBOr9jE,15108
shapely/lib.cpython-313-darwin.so,sha256=PNfaCjLf_tRLyGtNX3ZuR3i7Y-o9OLewOcT6VtJZhYU,271040
shapely/linear.py,sha256=r7g7Oirr13TxppORtGoI7RGBKoEfc5pfwIsQPYELn7E,8675
shapely/measurement.py,sha256=KJ5FWb87DuaF8qTR-jJ77-Y8GzH4z4TZ4WJRbSdHmVo,10622
shapely/ops.py,sha256=O3p4feybWouVbxx_OfOtphAPP6qZR61XzRy-IL7IxcQ,25679
shapely/plotting.py,sha256=2fuLnZdjw0NklD71VbEjJyRxoFaaKDv6FxXyF-iXJXs,6322
shapely/predicates.py,sha256=chSrPqgrahH8-1yVknoo4L_UsfoYWNXI9fdKsW-dZog,39673
shapely/prepared.py,sha256=Q97afxEhuqXMyxdgNgxzsoMjpYqiMzHXwn1HF8Wbmuk,2351
shapely/set_operations.py,sha256=vjhVKxibCXFkfJRFqxTPR8yV5bHxVu6kCYA3uqdvq7M,27273
shapely/speedups.py,sha256=vzWABtP1-3zBdX4bYZxzbwxLUgEsWjPTKAgGY3iGtag,1038
shapely/strtree.py,sha256=IqS-rPYu8bVPV50Hbf1GaItLOPKH172RL3Ze1Z9uPSI,20649
shapely/testing.py,sha256=CN2zAnObFnWsBBxxq-uMkmDTmKU0Rt0db0B8yQ3qAlc,6472
shapely/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/__pycache__/__init__.cpython-313.pyc,,
shapely/tests/__pycache__/common.cpython-313.pyc,,
shapely/tests/__pycache__/test_constructive.cpython-313.pyc,,
shapely/tests/__pycache__/test_coordinates.cpython-313.pyc,,
shapely/tests/__pycache__/test_coverage.cpython-313.pyc,,
shapely/tests/__pycache__/test_creation.cpython-313.pyc,,
shapely/tests/__pycache__/test_creation_indices.cpython-313.pyc,,
shapely/tests/__pycache__/test_decorators.cpython-313.pyc,,
shapely/tests/__pycache__/test_geometry.cpython-313.pyc,,
shapely/tests/__pycache__/test_io.cpython-313.pyc,,
shapely/tests/__pycache__/test_linear.cpython-313.pyc,,
shapely/tests/__pycache__/test_measurement.cpython-313.pyc,,
shapely/tests/__pycache__/test_misc.cpython-313.pyc,,
shapely/tests/__pycache__/test_plotting.cpython-313.pyc,,
shapely/tests/__pycache__/test_predicates.cpython-313.pyc,,
shapely/tests/__pycache__/test_ragged_array.cpython-313.pyc,,
shapely/tests/__pycache__/test_set_operations.cpython-313.pyc,,
shapely/tests/__pycache__/test_strtree.cpython-313.pyc,,
shapely/tests/__pycache__/test_testing.cpython-313.pyc,,
shapely/tests/common.py,sha256=hDiyxkeechWKNB631A4jYRZg1rNupovi1CLp_GK-iUE,10151
shapely/tests/geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/geometry/__pycache__/__init__.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_collection.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_coords.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_decimal.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_emptiness.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_equality.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_format.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_geometry_base.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_hash.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_linestring.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_multi.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_multilinestring.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_multipoint.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_multipolygon.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_point.cpython-313.pyc,,
shapely/tests/geometry/__pycache__/test_polygon.cpython-313.pyc,,
shapely/tests/geometry/test_collection.py,sha256=AremmkB08oV9nIDjVUkHFnLlDI97M6uj5dJxUW51Zo8,2669
shapely/tests/geometry/test_coords.py,sha256=EYUzCoy7EV7eKFBOcq8kNUKrqnL-lxLS73l8qbblcYw,3990
shapely/tests/geometry/test_decimal.py,sha256=AlHN5t3gzvvQ5w9iCIMvJsIibC4sdE3pfyQWpcBXI9c,2846
shapely/tests/geometry/test_emptiness.py,sha256=FIPHsZWLgRoehKpvoezAsqjjzJnosL7y08a-AbHlbA4,2362
shapely/tests/geometry/test_equality.py,sha256=qhe9DQpNAqRmO2bAb8sQvV0TuTI4FkXOTfeOHQVsEg0,9436
shapely/tests/geometry/test_format.py,sha256=GuNXkzeOFkGWFjaBhG0yIGHziXK-4-77AgmNMzPEGEU,4518
shapely/tests/geometry/test_geometry_base.py,sha256=2PiO4VfL3AQRQCVb2jRgo-H4Yg5pJ2J8yap023rcyR4,10726
shapely/tests/geometry/test_hash.py,sha256=EtQyaRUtTUIL8AiCjyvVC4KRJQ_E3uqr-4Wn_oaW-Ac,673
shapely/tests/geometry/test_linestring.py,sha256=ovKJ9LxcOGS_tASrst5imdYgLS9Gw8ydYlzTLTqiTt4,7043
shapely/tests/geometry/test_multi.py,sha256=Zaxc1MljTkpx82ySRlsURMcYH-pg1YmPh9Am_k2I59o,297
shapely/tests/geometry/test_multilinestring.py,sha256=Vd5oYTh0UP3jb5uJtUnOUjEFviTdgdRHE5LOyz1PVjw,2834
shapely/tests/geometry/test_multipoint.py,sha256=LCs7amyChhJwfWCkoipKTletSrLvm_t2PMM8i9T3mCo,3047
shapely/tests/geometry/test_multipolygon.py,sha256=nC7R2GWN82NINIoFk_4flWswKFnHM32uhGEvn4vLa7g,4907
shapely/tests/geometry/test_point.py,sha256=18Wn10MxH_YvUxdXbCLHc7L2DESxGBWxUxTjlJ5th84,5332
shapely/tests/geometry/test_polygon.py,sha256=RjwkejycT_Oiuwe3smoMYFZOCIY-jk8MEYIKrFYifuQ,15303
shapely/tests/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/legacy/__pycache__/__init__.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/conftest.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_affinity.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_box.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_buffer.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_cga.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_clip_by_rect.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_create_inconsistent_dimensionality.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_delaunay.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_empty_polygons.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_equality.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_geointerface.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_invalid_geometries.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_linear_referencing.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_linemerge.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_locale.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_make_valid.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_mapping.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_minimum_clearance.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_ndarrays.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_nearest.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_operations.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_operators.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_orient.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_parallel_offset.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_persist.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_pickle.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_polygonize.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_polylabel.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_predicates.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_prepared.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_products_z.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_shape.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_shared_paths.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_singularity.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_snap.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_split.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_substring.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_svg.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_transform.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_union.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_validation.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_vectorized.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_voronoi_diagram.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_wkb.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/test_wkt.cpython-313.pyc,,
shapely/tests/legacy/__pycache__/threading_test.cpython-313.pyc,,
shapely/tests/legacy/conftest.py,sha256=FvQ2x-Q2ZPMEmQJXwX0vKaKMWLm9OnF7nN3UsSEi-bA,334
shapely/tests/legacy/test_affinity.py,sha256=cAKrdDYgLfyM68x1g7e_RaZ4SHEG6fC__kNatC4mB4I,12483
shapely/tests/legacy/test_box.py,sha256=dWUFGKq_ItxQNw613IPqkAJu0vAbCKDSiXNtXfaU7yI,599
shapely/tests/legacy/test_buffer.py,sha256=BFNlI-dF7f05aoLUlMZ47Hhi1Q-O0a8Kn3Ope176ExI,6596
shapely/tests/legacy/test_cga.py,sha256=gjE9_4XqJEMX3QvKitDW9OKMvqHkvT0RLsukNNoxtfQ,1509
shapely/tests/legacy/test_clip_by_rect.py,sha256=Q9bgHlFxGnZG-yqttpv0qnWWbCh8kNWL7iSqPJZTAH0,4156
shapely/tests/legacy/test_create_inconsistent_dimensionality.py,sha256=Wr8rRg6053uVao4dPfO27MeM40cgMGeYAe5FTVaoz5U,10672
shapely/tests/legacy/test_delaunay.py,sha256=TubjNzbTxij9N9VDRX9Po_3MpxytonkCoAExq5aVaRs,838
shapely/tests/legacy/test_empty_polygons.py,sha256=pNjl0EVWuphek4TPHHqydPq10JqImQC0y963erL5K7E,683
shapely/tests/legacy/test_equality.py,sha256=HlvORMfrEdmuPIi88N27mRdqmeGluS7a5cv7VDpN_2A,747
shapely/tests/legacy/test_geointerface.py,sha256=dAsuOZSaVTZjWXquLlC0bA7MufQ8-Rr67J7FWQCfj4o,3648
shapely/tests/legacy/test_invalid_geometries.py,sha256=6Cdas1Z2Au6ZDqjO7LNRTU002y3fLHvPTFtFnBmM6HY,891
shapely/tests/legacy/test_linear_referencing.py,sha256=CBHU1SXOHor7WKGww1ZmoSgLqa0QsuhfVPK21aiNv0Y,2914
shapely/tests/legacy/test_linemerge.py,sha256=6jyoJbj1ZOvRbqFW6Cc7bS9G7DHCkKN-sxAUiAMolsQ,1329
shapely/tests/legacy/test_locale.py,sha256=RbH6dY44qDonZWRuYkqdl86kku5c9G1ZPH39VumT88Y,1434
shapely/tests/legacy/test_make_valid.py,sha256=qZVvJ4-N6TSrJz-LxL0V8HN_c_s3CYtiCzRfbK_xlxs,479
shapely/tests/legacy/test_mapping.py,sha256=CWq3y5US69POt8d_q1PFhMJy5AY54fAl4uMDsbMQH4s,395
shapely/tests/legacy/test_minimum_clearance.py,sha256=uXnBqMisUz5zgoZAsurfizsPGutgr2u63y6BqmqA9GQ,694
shapely/tests/legacy/test_ndarrays.py,sha256=5_sHeWOJ49rt41pLl5j41IbWFDDcSW0kAFqtC45jKkU,1244
shapely/tests/legacy/test_nearest.py,sha256=eXHfHQA0k1cUYNcn9WvL9SpnwhCjFb2CeN2NAMHQ0M0,476
shapely/tests/legacy/test_operations.py,sha256=RYhfq-OWNI5zRuq-RNuDaHt2Dmn8yX6wK1yYejJaTAY,4307
shapely/tests/legacy/test_operators.py,sha256=aWjGgHOmLm6Jx21BiR9APBFokL4hxOrC1IRY8YHo0QU,1948
shapely/tests/legacy/test_orient.py,sha256=i2g2Iez4Ceu1IRJnzA04hS6FMOo62GW-j956zd7xOqY,3515
shapely/tests/legacy/test_parallel_offset.py,sha256=GRiJa48w2-z7NXDVgjyh5wPTCt_lQZg5l_b1bExDZ4U,2332
shapely/tests/legacy/test_persist.py,sha256=4oUuI953DcRHZRvJABanw38iLhJr_KyATL52t1Pt3Bg,1710
shapely/tests/legacy/test_pickle.py,sha256=kEZJ_HRrJvplR9TvL94ZhNiFn3uuisfQFgrx79S_i4g,2368
shapely/tests/legacy/test_polygonize.py,sha256=QTe3FuJk397uJselWzw3GDtPPYo_n8Qb10Fmq-kO_P4,1388
shapely/tests/legacy/test_polylabel.py,sha256=Kd9uoVs41uqsMtcEZg-0i9wwwAtVoAerBR7IbUK3OI0,3143
shapely/tests/legacy/test_predicates.py,sha256=rK9ruVT6tqWsTER21tJKMy1-9ic68XgGKBBGRTeTUVg,2958
shapely/tests/legacy/test_prepared.py,sha256=ImLBEnXk0ZJZLe6jcHtxhNJBgJrK9GuwQzA9Btcdvx8,2476
shapely/tests/legacy/test_products_z.py,sha256=CrN95z-8sl3J76YZE05E5tYvMLrhGDTp_Rivbblbq5g,388
shapely/tests/legacy/test_shape.py,sha256=Mcv6027NrA9jYIfbCbcgA968HjHWl5Cf4tf8cq4qGWo,1809
shapely/tests/legacy/test_shared_paths.py,sha256=KZ1FM6s2O315voIw1XOEluwMnfzhyLd91yXnpNmnt7Y,1434
shapely/tests/legacy/test_singularity.py,sha256=GI-_673c18SIAq1UQZT3ZOnfeSbhBEuGAYgCuzPldho,381
shapely/tests/legacy/test_snap.py,sha256=fCWAaneOQn5rxmZySdiNI7LDwtZaqfYOLGseoJYRV6Q,763
shapely/tests/legacy/test_split.py,sha256=7SwUuaz8-VkuKHKAvCpcJeAoEbQaCMj5P73AmnA0jHU,11331
shapely/tests/legacy/test_substring.py,sha256=1SimMTaVc34uk84ylVSriJNx4T2Ah0axebz9IFbFZaU,21162
shapely/tests/legacy/test_svg.py,sha256=xIXhkL44w1PJl3suxvDjXKd9nPO7ljkYMTz3qoD7DG0,8253
shapely/tests/legacy/test_transform.py,sha256=s5GWsu9EeW__D0QHT4L9ivr1lygeiSxRNLEyGgmdksk,2705
shapely/tests/legacy/test_union.py,sha256=MJp3Lsufq1MPOl59hGtQq3w-H2U3VmmDMxhL_Z9c8Zc,2094
shapely/tests/legacy/test_validation.py,sha256=L33s2pjag9R_HoDa-mUG1oEOZcK-pexhnQHX4xlZu6I,238
shapely/tests/legacy/test_vectorized.py,sha256=EHTI4KYl3RAlzM2O9BuZfRLv2kxznutJtGWMubJYl8g,3723
shapely/tests/legacy/test_voronoi_diagram.py,sha256=z3ZK8Vk0NkMCtLG50vPSvQucBAaTHw3GH9sk1l2b8qE,4080
shapely/tests/legacy/test_wkb.py,sha256=npCjNYrtvFv2nhwtAciAj2te7pf1pb1g8RMrKxnBr2M,5558
shapely/tests/legacy/test_wkt.py,sha256=j0AbQI2QSktLjmSbBn7ZigvwWtN_EsNqxoKq7UAKQRU,1600
shapely/tests/legacy/threading_test.py,sha256=lWyBDOwysMaoPok5xGvDNv7CUn5jkEdDUu3bILUYqzA,1004
shapely/tests/test_constructive.py,sha256=0fKiYrfG1O3LZyDkkToLZzd_6EWshoGVqW4dsKXRlVQ,46079
shapely/tests/test_coordinates.py,sha256=7T9veZS6UCOv_bkZvymSGzCb6lLaLM0xdLXy9qUMWQw,13294
shapely/tests/test_coverage.py,sha256=_A7luPwyh2RPHFIpjd4rbo9Y0wU5VBeJ6mKHOWyUhHo,9578
shapely/tests/test_creation.py,sha256=is-CNyOwFcr7XI7vCr0bZQMX-kxlyx_VR-r7u07mOto,22345
shapely/tests/test_creation_indices.py,sha256=RC4AyfZJffy4KADDPmRKZTufaGsuwrrShX6dcxDwYQA,18597
shapely/tests/test_decorators.py,sha256=8ilvycBR-Czql8Oz-mMdKi40yFaqJyTl2uHS5eBv6mI,3699
shapely/tests/test_geometry.py,sha256=FnWY-s9Tdbg6J4CUqm-vfATlbURcgN9RIKzv_h22Qrw,23821
shapely/tests/test_io.py,sha256=Rj-fXhoyAJJQH91iZ4AM_ulbtK4FYhkDHu0ZInDYiaE,50062
shapely/tests/test_linear.py,sha256=EpAyncRrvIb91V1OT1FM6Mr8_CaohkRjaOwAii_v4vw,7726
shapely/tests/test_measurement.py,sha256=HUl5S9aVA_V6RSOa-xk10_XPxjJpVcvjY9XrB7epq88,9948
shapely/tests/test_misc.py,sha256=UcccpoSvNf9E2zJBrsICxkI5_zZBFdmtdyn5B-qWrFs,5394
shapely/tests/test_plotting.py,sha256=pnq6CRfHBKZSz1Q1vLAjIllht--yZmDXngTXQOu8J88,4043
shapely/tests/test_predicates.py,sha256=rIf7QAA8a4sD6pqESEwy4aZdd0LB1bPW7XhnjcR_pzo,13714
shapely/tests/test_ragged_array.py,sha256=aJE7PsoP6gWghh39en1bOSz9Ye3Y88h_XE5ptsI_e9M,21067
shapely/tests/test_set_operations.py,sha256=teWSwLegy1tOmBY0PBZPpCyOOvOKFmlwd19bOR88qys,17476
shapely/tests/test_strtree.py,sha256=049gkeywCzpVdXQoFv-noDhjMpQt80lzc2GYDeXHfRU,73511
shapely/tests/test_testing.py,sha256=fOWFjo3MyHCA_sEVjQanZ-_FNsQtm7Ng8hZgWQatM8A,2846
shapely/validation.py,sha256=AivYbY--pi2eV9hVoTRAeOwtS69m4S39w-wl3bRcPoQ,1471
shapely/vectorized/__init__.py,sha256=4FDHw--KsI8YPosvaEdtmR14BYod4CCwPqfwDmpfitU,2812
shapely/vectorized/__pycache__/__init__.cpython-313.pyc,,
shapely/wkb.py,sha256=Ne_kT9ZnEn7haUA674eUuWa7Dztfu55HgjHdKFn-1p4,1937
shapely/wkt.py,sha256=AmJ0Sv-EMUDKYKWxWqZbP_zqgkj1ORQBcD-0TnHwzyA,1733
