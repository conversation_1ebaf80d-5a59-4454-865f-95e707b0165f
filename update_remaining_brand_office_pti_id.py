#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script cập nhật pti_id cho các bản ghi brand_office chưa có pti_id
- Chỉ xử lý các bản ghi có pti_id IS NULL
- Tracking thời gian và thống kê chi tiết
- T<PERSON><PERSON> báo cáo hoàn chỉnh cho Jira task
"""

import json
import subprocess
import sys
import time
import datetime
from shapely.geometry import Point, shape
from shapely.ops import unary_union

# Database config
DB_CONFIG = {
    'host': 'localhost',
    'port': '3306',
    'user': 'root',
    'password': 'root',
    'database': 'urbox'
}

class BrandOfficeGeocoder:
    def __init__(self):
        self.ward_geometries = {}
        self.processed_count = 0
        self.updated_count = 0
        self.error_count = 0
        self.no_geometry_count = 0
        self.start_time = None
        self.end_time = None
        self.total_records = 0
        
    def execute_mysql_query(self, query):
        """Thực thi MySQL query và trả về kết quả"""
        try:
            cmd = [
                'mysql', 
                f'-u{DB_CONFIG["user"]}', 
                f'-p{DB_CONFIG["password"]}',
                f'-h{DB_CONFIG["host"]}', 
                f'-P{DB_CONFIG["port"]}',
                f'-D{DB_CONFIG["database"]}',
                '--batch', '--raw',
                '-e', query
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                print(f"❌ MySQL Error: {result.stderr}")
                return None
                
            return result.stdout.strip()
            
        except Exception as e:
            print(f"❌ Exception executing query: {e}")
            return None
    
    def load_ward_geometries(self):
        """Load tất cả geometry data từ ward_geometry"""
        print("🔄 Loading ward geometries...")
        
        query = "SELECT pti_id, data FROM ward_geometry WHERE data IS NOT NULL"
        result = self.execute_mysql_query(query)
        
        if not result:
            print("❌ Không thể load ward geometries")
            return False
            
        lines = result.split('\n')
        if len(lines) < 2:
            print("❌ Không có dữ liệu ward geometry")
            return False
            
        # Skip header line
        for line in lines[1:]:
            if not line.strip():
                continue
                
            parts = line.split('\t', 1)
            if len(parts) != 2:
                continue
                
            pti_id = int(parts[0])
            geometry_json = parts[1]
            
            try:
                geometry_data = json.loads(geometry_json)
                
                # Parse GeoJSON FeatureCollection
                if geometry_data.get('type') == 'FeatureCollection':
                    features = geometry_data.get('features', [])
                    polygons = []
                    
                    for feature in features:
                        if feature.get('geometry', {}).get('type') in ['Polygon', 'MultiPolygon']:
                            try:
                                polygon = shape(feature['geometry'])
                                if polygon.is_valid:
                                    polygons.append(polygon)
                            except Exception as e:
                                print(f"⚠️ Error parsing geometry for pti_id {pti_id}: {e}")
                                continue
                    
                    if polygons:
                        # Combine all polygons into one
                        combined_polygon = unary_union(polygons)
                        self.ward_geometries[pti_id] = combined_polygon
                        
            except Exception as e:
                print(f"⚠️ Error processing pti_id {pti_id}: {e}")
                continue
        
        print(f"✅ Loaded {len(self.ward_geometries)} ward geometries")
        return True
    
    def get_remaining_brand_office_records(self):
        """Lấy danh sách brand_office chưa có pti_id"""
        print("🔄 Loading brand_office records without pti_id...")
        
        # Chỉ lấy các bản ghi chưa có pti_id và có tọa độ trong phạm vi Việt Nam
        query = """
        SELECT id, latitude, longitude 
        FROM brand_office 
        WHERE pti_id IS NULL
        AND latitude BETWEEN 8 AND 24 
        AND longitude BETWEEN 102 AND 110
        AND latitude != 0 AND longitude != 0
        """
            
        result = self.execute_mysql_query(query)
        
        if not result:
            print("❌ Không thể load brand_office records")
            return []
            
        records = []
        lines = result.split('\n')
        
        # Skip header line
        for line in lines[1:]:
            if not line.strip():
                continue
                
            parts = line.split('\t')
            if len(parts) >= 3:
                try:
                    record = {
                        'id': int(parts[0]),
                        'latitude': float(parts[1]),
                        'longitude': float(parts[2])
                    }
                    records.append(record)
                except ValueError as e:
                    print(f"⚠️ Error parsing record: {line} - {e}")
                    continue
        
        self.total_records = len(records)
        print(f"✅ Found {self.total_records} brand_office records without pti_id")
        return records
    
    def find_pti_id_for_point(self, latitude, longitude):
        """Tìm pti_id cho một điểm tọa độ"""
        point = Point(longitude, latitude)  # Note: Shapely uses (x, y) = (longitude, latitude)
        
        for pti_id, geometry in self.ward_geometries.items():
            try:
                if geometry.contains(point):
                    return pti_id
            except Exception as e:
                print(f"⚠️ Error checking point in geometry {pti_id}: {e}")
                continue
                
        return None
    
    def update_brand_office_pti_id(self, office_id, pti_id):
        """Cập nhật pti_id cho brand_office"""
        query = f"UPDATE brand_office SET pti_id = {pti_id} WHERE id = {office_id}"
        result = self.execute_mysql_query(query)
        return result is not None
    
    def get_processing_statistics(self):
        """Lấy thống kê chi tiết"""
        duration = self.end_time - self.start_time if self.end_time and self.start_time else datetime.timedelta(0)
        duration_seconds = duration.total_seconds()
        success_rate = (self.updated_count / self.total_records * 100) if self.total_records > 0 else 0

        return {
            'total_records': self.total_records,
            'processed_count': self.processed_count,
            'updated_count': self.updated_count,
            'no_geometry_count': self.no_geometry_count,
            'error_count': self.error_count,
            'success_rate': round(success_rate, 2),
            'duration_seconds': round(duration_seconds, 2),
            'duration_formatted': str(datetime.timedelta(seconds=int(duration_seconds))),
            'records_per_second': round(self.processed_count / duration_seconds, 2) if duration_seconds > 0 else 0,
            'start_time': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None
        }
    
    def process_remaining_records(self):
        """Xử lý các bản ghi brand_office chưa có pti_id"""
        print("🚀 Bắt đầu xử lý brand_office records chưa có pti_id...")
        self.start_time = datetime.datetime.now()
        
        # Load ward geometries
        if not self.load_ward_geometries():
            return False
            
        # Load brand_office records without pti_id
        records = self.get_remaining_brand_office_records()
        if not records:
            print("✅ Không có bản ghi nào cần xử lý!")
            return True
            
        print(f"🔄 Xử lý {len(records)} records...")
        
        for i, record in enumerate(records):
            self.processed_count += 1
            
            # Progress indicator
            if self.processed_count % 100 == 0:
                progress = (self.processed_count / len(records)) * 100
                print(f"📊 Tiến độ: {self.processed_count}/{len(records)} ({progress:.1f}%) - Updated: {self.updated_count} - No geometry: {self.no_geometry_count} - Errors: {self.error_count}")
            
            try:
                # Tìm pti_id cho tọa độ này
                pti_id = self.find_pti_id_for_point(record['latitude'], record['longitude'])
                
                if pti_id:
                    # Cập nhật database
                    if self.update_brand_office_pti_id(record['id'], pti_id):
                        self.updated_count += 1
                        if self.updated_count <= 10:  # Chỉ log 10 bản ghi đầu tiên
                            print(f"✅ Updated ID {record['id']}: pti_id = {pti_id}")
                    else:
                        self.error_count += 1
                        print(f"❌ Failed to update ID {record['id']}")
                else:
                    self.no_geometry_count += 1
                    if self.no_geometry_count <= 10:  # Chỉ log 10 bản ghi đầu tiên
                        print(f"⚠️ No geometry found for ID {record['id']} at ({record['latitude']}, {record['longitude']})")
                    
            except Exception as e:
                self.error_count += 1
                print(f"❌ Error processing ID {record['id']}: {e}")
                
            # Small delay to avoid overwhelming the database
            time.sleep(0.01)
        
        self.end_time = datetime.datetime.now()
        
        # In thống kê cuối cùng
        stats = self.get_processing_statistics()
        print(f"\n🎉 Hoàn thành!")
        print(f"📊 Thống kê chi tiết:")
        print(f"   - Tổng số bản ghi cần xử lý: {stats['total_records']:,}")
        print(f"   - Đã xử lý: {stats['processed_count']:,}")
        print(f"   - Cập nhật thành công: {stats['updated_count']:,}")
        print(f"   - Không tìm thấy geometry: {stats['no_geometry_count']:,}")
        print(f"   - Lỗi: {stats['error_count']:,}")
        print(f"   - Tỷ lệ thành công: {stats['success_rate']}%")
        print(f"   - Thời gian xử lý: {stats['duration_formatted']}")
        print(f"   - Tốc độ: {stats['records_per_second']} records/giây")
        print(f"   - Bắt đầu: {stats['start_time']}")
        print(f"   - Kết thúc: {stats['end_time']}")
        
        return True

def main():
    """Main function"""
    print("🚀 Brand Office PTI_ID Updater - Remaining Records")
    print("=" * 60)
    
    # Kiểm tra thư viện Shapely
    try:
        from shapely.geometry import Point
        print("✅ Shapely library available")
    except ImportError:
        print("❌ Shapely library not found. Please install: pip install shapely")
        return False
    
    geocoder = BrandOfficeGeocoder()
    
    # Xử lý các bản ghi chưa có pti_id
    success = geocoder.process_remaining_records()
    
    if success:
        # Lưu thống kê vào file để sử dụng cho Jira task
        stats = geocoder.get_processing_statistics()
        with open('brand_office_update_stats.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"\n💾 Thống kê đã được lưu vào file: brand_office_update_stats.json")
        
        return stats
    else:
        print("❌ Xử lý không thành công.")
        return None

if __name__ == "__main__":
    main()
