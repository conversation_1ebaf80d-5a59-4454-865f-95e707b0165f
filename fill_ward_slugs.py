#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để fill dữ liệu slug cho bảng ward từ trường title
- slug: từ title, bỏ dấu tiếng <PERSON>, chuyển thành chữ thường, thay khoảng trắng bằng gạch ngang
"""

import mysql.connector
import re
import time

def remove_vietnamese_accents(text):
    """Bỏ dấu tiếng Việt"""
    vietnamese_map = {
        'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
        'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
        'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
        'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
        'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd',
        'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
        'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
        'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
        'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
        'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
        'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
        'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
        'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
        'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
        'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
        'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
        'Ỳ': 'Y', 'Ý': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y', 'Ỵ': 'Y',
        'Đ': 'D'
    }
    
    result = ''
    for char in text:
        result += vietnamese_map.get(char, char)
    
    return result

def create_slug(title):
    """Tạo slug từ title"""
    if not title:
        return ''
    
    # Chuyển thành chữ thường
    clean_title = title.lower()
    
    # Bỏ dấu tiếng Việt
    clean_title = remove_vietnamese_accents(clean_title)
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    
    # Bỏ dấu gạch ngang ở đầu và cuối
    clean_title = clean_title.strip('-')
    
    return clean_title

def main():
    """Main function"""
    print("🚀 Bắt đầu fill dữ liệu slug cho bảng ward từ trường title...")
    
    # Kết nối database
    try:
        conn = mysql.connector.connect(
            host='127.0.0.1',
            user='root',
            password='root',
            database='urbox',
            charset='utf8mb4',
            autocommit=False
        )
        cursor = conn.cursor()
        print("✅ Kết nối database thành công!")
        
        # Test connection
        cursor.execute("SELECT COUNT(*) FROM ward")
        count = cursor.fetchone()[0]
        print(f"📊 Tìm thấy {count} records trong bảng ward")
        
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return
    
    try:
        # Lấy dữ liệu từ bảng ward
        print("📊 Lấy dữ liệu từ bảng ward...")
        cursor.execute("SELECT id, title FROM ward ORDER BY id")
        records = cursor.fetchall()
        
        print(f"📝 Tìm thấy {len(records)} records trong bảng ward")
        
        # Xử lý từng record
        updated_count = 0
        error_count = 0
        
        for record in records:
            record_id, title = record
            
            try:
                # Tạo slug
                slug = create_slug(title)
                
                # Update database
                update_sql = """
                UPDATE ward 
                SET slug = %s 
                WHERE id = %s
                """
                
                cursor.execute(update_sql, (slug, record_id))
                updated_count += 1
                
                if updated_count % 500 == 0:
                    print(f"📝 Đã update {updated_count} records...")
                    conn.commit()  # Commit mỗi 500 records
                
            except Exception as e:
                print(f"❌ Lỗi update record ID {record_id}: {e}")
                error_count += 1
                continue
        
        # Commit cuối cùng
        conn.commit()
        
        print(f"\n🎉 Hoàn thành!")
        print(f"✅ Đã update thành công: {updated_count} records")
        print(f"❌ Lỗi: {error_count} records")
        
        # Hiển thị một vài ví dụ
        print("\n📋 Một vài ví dụ kết quả:")
        cursor.execute("""
        SELECT id, title, slug 
        FROM ward 
        WHERE slug IS NOT NULL AND slug != ''
        ORDER BY id 
        LIMIT 10
        """)
        
        examples = cursor.fetchall()
        for example in examples:
            record_id, title, slug = example
            print(f"ID {record_id}: '{title}' -> '{slug}'")
        
        # Thống kê theo độ dài slug
        print("\n📊 Thống kê slug:")
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN slug IS NOT NULL AND slug != '' THEN 1 END) as has_slug,
            COUNT(CASE WHEN slug IS NULL OR slug = '' THEN 1 END) as no_slug
        FROM ward
        """)
        
        stats = cursor.fetchone()
        total, has_slug, no_slug = stats
        print(f"📈 Tổng records: {total}")
        print(f"✅ Có slug: {has_slug}")
        print(f"❌ Không có slug: {no_slug}")
        
    except Exception as e:
        print(f"❌ Lỗi xử lý: {e}")
        conn.rollback()
    
    finally:
        cursor.close()
        conn.close()
        print("🔒 Đã đóng kết nối database")

if __name__ == "__main__":
    main()
