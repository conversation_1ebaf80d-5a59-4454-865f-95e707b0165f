#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tổng hợp để chạy toàn bộ quy trình cập nhật pti_id cho brand_office
- Chạy script cập nhật các bản ghi còn lại
- <PERSON><PERSON><PERSON> báo cáo chi tiết
- Hi<PERSON>n thị thống kê tổng quan
"""

import subprocess
import json
import os
import datetime

def execute_command(command, description):
    """Thực thi command và hiển thị kết quả"""
    print(f"\n🔄 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd='/Users/<USER>/webroot/satnhap')
        
        if result.returncode == 0:
            print(f"✅ {description} - Thành công!")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} - Thất bại!")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def get_database_stats():
    """Lấy thống kê từ database"""
    query = """
    SELECT 
        COUNT(*) AS total_records,
        SUM(CASE WHEN pti_id IS NOT NULL THEN 1 ELSE 0 END) AS with_pti_id,
        SUM(CASE WHEN pti_id IS NULL THEN 1 ELSE 0 END) AS without_pti_id,
        SUM(CASE WHEN latitude != 0 AND longitude != 0 THEN 1 ELSE 0 END) AS with_coordinates,
        SUM(CASE WHEN latitude != 0 AND longitude != 0 AND pti_id IS NOT NULL THEN 1 ELSE 0 END) AS with_coordinates_and_pti_id,
        SUM(CASE WHEN latitude != 0 AND longitude != 0 AND pti_id IS NULL THEN 1 ELSE 0 END) AS with_coordinates_without_pti_id
    FROM brand_office
    """
    
    cmd = f'mysql -uroot -proot -hlocalhost -P3306 -Durbox --batch --raw -e "{query}"'
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None
        
        parts = lines[1].split('\t')
        if len(parts) < 6:
            return None
        
        return {
            'total_records': int(parts[0]),
            'with_pti_id': int(parts[1]),
            'without_pti_id': int(parts[2]),
            'with_coordinates': int(parts[3]),
            'with_coordinates_and_pti_id': int(parts[4]),
            'with_coordinates_without_pti_id': int(parts[5])
        }
    except Exception as e:
        print(f"❌ Error getting database stats: {e}")
        return None

def display_final_report():
    """Hiển thị báo cáo cuối cùng"""
    print("\n" + "="*80)
    print("🎉 BÁO CÁO TỔNG KẾT CẬP NHẬT PTI_ID CHO BRAND_OFFICE")
    print("="*80)
    
    # Lấy thống kê database
    db_stats = get_database_stats()
    if not db_stats:
        print("❌ Không thể lấy thống kê từ database!")
        return
    
    # Lấy thống kê từ file
    stats_file = '/Users/<USER>/webroot/satnhap/brand_office_update_stats.json'
    if os.path.exists(stats_file):
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                update_stats = json.load(f)
        except:
            update_stats = None
    else:
        update_stats = None
    
    # Hiển thị thống kê tổng quan
    print(f"\n📊 THỐNG KÊ TỔNG QUAN:")
    print(f"   • Tổng số bản ghi brand_office: {db_stats['total_records']:,}")
    print(f"   • Số bản ghi có tọa độ: {db_stats['with_coordinates']:,}")
    print(f"   • Số bản ghi đã có pti_id: {db_stats['with_pti_id']:,}")
    print(f"   • Số bản ghi chưa có pti_id: {db_stats['without_pti_id']:,}")
    
    # Tính tỷ lệ thành công
    success_rate = (db_stats['with_coordinates_and_pti_id'] / db_stats['with_coordinates'] * 100) if db_stats['with_coordinates'] > 0 else 0
    print(f"   • Tỷ lệ cập nhật thành công: {success_rate:.2f}%")
    
    # Hiển thị thống kê script cuối cùng
    if update_stats:
        print(f"\n📈 THỐNG KÊ SCRIPT CUỐI CÙNG:")
        print(f"   • Bản ghi cần xử lý: {update_stats['total_records']:,}")
        print(f"   • Đã xử lý: {update_stats['processed_count']:,}")
        print(f"   • Cập nhật thành công: {update_stats['updated_count']:,}")
        print(f"   • Không tìm thấy geometry: {update_stats['no_geometry_count']:,}")
        print(f"   • Thời gian xử lý: {update_stats['duration_formatted']}")
        print(f"   • Tốc độ: {update_stats['records_per_second']} records/giây")
    
    print(f"\n✅ KẾT LUẬN:")
    print(f"   Đã hoàn thành cập nhật pti_id cho {db_stats['with_coordinates_and_pti_id']:,}/{db_stats['with_coordinates']:,} bản ghi")
    print(f"   có tọa độ với tỷ lệ thành công {success_rate:.2f}%")
    
    # Hiển thị files đã tạo
    print(f"\n📁 FILES ĐÃ TẠO:")
    files = [
        'update_brand_office_pti_id.py',
        'update_remaining_brand_office_pti_id.py', 
        'create_jira_report.py',
        'brand_office_update_stats.json',
        'BAO_CAO_CAP_NHAT_PTI_ID_BRAND_OFFICE.md'
    ]
    
    for file in files:
        if os.path.exists(f'/Users/<USER>/webroot/satnhap/{file}'):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")

def main():
    """Main function"""
    print("🚀 COMPLETE BRAND OFFICE PTI_ID UPDATE PROCESS")
    print("=" * 80)
    print(f"Thời gian bắt đầu: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Bước 1: Chạy script cập nhật các bản ghi còn lại
    success1 = execute_command(
        "source venv/bin/activate && python3 update_remaining_brand_office_pti_id.py",
        "Cập nhật các bản ghi brand_office còn lại"
    )
    
    if not success1:
        print("❌ Script cập nhật thất bại! Dừng quy trình.")
        return False
    
    # Bước 2: Hiển thị báo cáo cuối cùng
    display_final_report()
    
    print(f"\n🎉 Hoàn thành toàn bộ quy trình!")
    print(f"Thời gian kết thúc: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"\n📋 Để tạo Jira task, vui lòng sử dụng nội dung trong file:")
    print(f"   BAO_CAO_CAP_NHAT_PTI_ID_BRAND_OFFICE.md")
    
    return True

if __name__ == "__main__":
    main()
