từ bảng ward_geometry đã có thông tin maxa. thông tin này sẽ map 1-1 với xaphuong.maxa.
dựa vào đây hãy fill data vào các cột ward_pti_id, province_pti_id trong bảng ward_geometry.
ward_pti_id dượ<PERSON> lấy từ ward.pti_id,
province_pti_id dượ<PERSON> lấy từ xaphuong.matinh

<PERSON>u khi fill xong, tiế<PERSON> tục fill tiếp các cột ward_title, province_title trong bảng ward_geometry.
province_title dược lấy từ ___province.title với điều kiện ___province.pti_id=ward_geometry.province_pti_id
ward_title dư<PERSON><PERSON> lấy từ ward.title với điều kiện ward.title=xaphuong.tenhc

Cuối cùng tạo file tổng kết để tôi tự tạo task jira.Tô<PERSON> cần trình bày g<PERSON><PERSON><PERSON>h<PERSON>, mô tả về cách làm, gi<PERSON>i pháp xử lý việc đồng bộ data kèm kết quả của việc xử lý: ch<PERSON>y bao nhiêu bản ghi, bao nhiêu thành công, thất bại, tỷ lệ thành công, thời gian chạy
liệt kê ra các file liên quan tới task này.