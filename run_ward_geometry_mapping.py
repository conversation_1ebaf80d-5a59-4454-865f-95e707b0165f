#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT FILL DỮ LIỆU MAPPING CHO BẢNG WARD_GEOMETRY
=================================================
Dự án: Cập nhật hệ thống hành chính VN
Tạo: 2025-07-15
Mục đích: Chạy script SQL và tạo báo cáo tổng kết cho Jira task

Chức năng:
1. Fill ward_pti_id, province_pti_id từ bảng xaphuong
2. Fill ward_title, province_title từ bảng ward và ___province
3. Tạo báo cáo chi tiết với thống kê đầy đủ
"""

import subprocess
import json
import time
from datetime import datetime
import os

class WardGeometryMappingProcessor:
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'root',
            'database': 'urbox'
        }
        self.start_time = None
        self.end_time = None
        self.stats = {}
        
    def run_mysql_command(self, sql_command):
        """Chạy lệnh MySQL và trả về kết quả"""
        try:
            cmd = [
                'mysql',
                '-u', self.db_config['user'],
                '-p' + self.db_config['password'],
                '-h', self.db_config['host'],
                '-P', self.db_config['port'],
                '-D', self.db_config['database'],
                '-e', sql_command
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi MySQL: {result.stderr}")
                return None
            return result.stdout.strip()
        except Exception as e:
            print(f"❌ Lỗi chạy MySQL: {e}")
            return None
    
    def run_sql_file(self, sql_file_path):
        """Chạy file SQL"""
        try:
            cmd = [
                'mysql',
                '-u', self.db_config['user'],
                '-p' + self.db_config['password'],
                '-h', self.db_config['host'],
                '-P', self.db_config['port'],
                '-D', self.db_config['database']
            ]
            
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                result = subprocess.run(cmd, stdin=f, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Lỗi chạy SQL file: {result.stderr}")
                return False
            
            print(f"✅ Chạy SQL file thành công: {sql_file_path}")
            return True
        except Exception as e:
            print(f"❌ Lỗi chạy SQL file: {e}")
            return False
    
    def collect_statistics(self):
        """Thu thập thống kê chi tiết"""
        print("📊 Thu thập thống kê...")
        
        # Thống kê tổng quan
        sql = """
        SELECT 
            COUNT(*) AS total_records,
            COUNT(CASE WHEN ward_pti_id IS NOT NULL THEN 1 END) AS has_ward_pti_id,
            COUNT(CASE WHEN province_pti_id IS NOT NULL THEN 1 END) AS has_province_pti_id,
            COUNT(CASE WHEN ward_title IS NOT NULL THEN 1 END) AS has_ward_title,
            COUNT(CASE WHEN province_title IS NOT NULL THEN 1 END) AS has_province_title,
            COUNT(CASE WHEN ward_pti_id IS NOT NULL AND province_pti_id IS NOT NULL 
                       AND ward_title IS NOT NULL AND province_title IS NOT NULL THEN 1 END) AS complete_records
        FROM ward_geometry;
        """
        
        result = self.run_mysql_command(sql)
        if result:
            lines = result.split('\n')
            if len(lines) > 1:
                values = lines[1].split('\t')
                if len(values) >= 6:
                    self.stats['total_records'] = int(values[0])
                    self.stats['has_ward_pti_id'] = int(values[1])
                    self.stats['has_province_pti_id'] = int(values[2])
                    self.stats['has_ward_title'] = int(values[3])
                    self.stats['has_province_title'] = int(values[4])
                    self.stats['complete_records'] = int(values[5])
                    
                    # Tính tỷ lệ thành công
                    if self.stats['total_records'] > 0:
                        self.stats['success_rate'] = round(
                            (self.stats['complete_records'] / self.stats['total_records']) * 100, 2
                        )
                    else:
                        self.stats['success_rate'] = 0
        
        # Thống kê theo province
        sql_province = """
        SELECT 
            province_pti_id,
            province_title,
            COUNT(*) AS count
        FROM ward_geometry 
        WHERE province_pti_id IS NOT NULL 
        GROUP BY province_pti_id, province_title 
        ORDER BY province_pti_id;
        """
        
        result_province = self.run_mysql_command(sql_province)
        if result_province:
            lines = result_province.split('\n')[1:]  # Bỏ header
            self.stats['province_breakdown'] = []
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        self.stats['province_breakdown'].append({
                            'province_pti_id': parts[0],
                            'province_title': parts[1],
                            'count': int(parts[2])
                        })
        
        # Kiểm tra records có vấn đề
        sql_issues = """
        SELECT 
            COUNT(*) AS total_issues,
            COUNT(CASE WHEN ward_pti_id IS NULL THEN 1 END) AS missing_ward_pti_id,
            COUNT(CASE WHEN province_pti_id IS NULL THEN 1 END) AS missing_province_pti_id,
            COUNT(CASE WHEN ward_title IS NULL THEN 1 END) AS missing_ward_title,
            COUNT(CASE WHEN province_title IS NULL THEN 1 END) AS missing_province_title
        FROM ward_geometry
        WHERE ward_pti_id IS NULL OR province_pti_id IS NULL 
           OR ward_title IS NULL OR province_title IS NULL;
        """
        
        result_issues = self.run_mysql_command(sql_issues)
        if result_issues:
            lines = result_issues.split('\n')
            if len(lines) > 1:
                values = lines[1].split('\t')
                if len(values) >= 5:
                    self.stats['issues'] = {
                        'total_issues': int(values[0]),
                        'missing_ward_pti_id': int(values[1]),
                        'missing_province_pti_id': int(values[2]),
                        'missing_ward_title': int(values[3]),
                        'missing_province_title': int(values[4])
                    }
    
    def generate_report(self):
        """Tạo báo cáo tổng kết"""
        report_content = f"""# BÁO CÁO FILL DỮ LIỆU MAPPING CHO BẢNG WARD_GEOMETRY

## THÔNG TIN CHUNG
- **Dự án**: Cập nhật hệ thống hành chính VN
- **Task**: Fill dữ liệu mapping ward_geometry
- **Thời gian thực hiện**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}
- **Thời gian chạy**: {(self.end_time - self.start_time).total_seconds():.2f} giây
- **Database**: urbox

## GIẢI PHÁP VÀ CÁCH THỰC HIỆN

### Mô tả giải pháp
1. **Mapping dữ liệu 1-1**: Sử dụng ward_geometry.maxa để map với xaphuong.maxa
2. **Fill ward_pti_id và province_pti_id**: Lấy từ xaphuong.ma và xaphuong.matinh
3. **Fill ward_title và province_title**: Lấy từ bảng ward và ___province thông qua PTI ID

### Các bước thực hiện
1. **Kiểm tra cấu trúc**: Kiểm tra và thêm các cột cần thiết vào ward_geometry
2. **Mapping xaphuong**: Fill ward_pti_id và province_pti_id từ bảng xaphuong
3. **Fill ward_title**: Lấy title từ bảng ward với điều kiện ward.pti_id = ward_geometry.ward_pti_id
4. **Fill province_title**: Lấy title từ bảng ___province với điều kiện ___province.pti_id = ward_geometry.province_pti_id

### Xử lý đồng bộ dữ liệu
- **Mapping 1-1**: ward_geometry.maxa ↔ xaphuong.maxa
- **Điều kiện lọc**: Chỉ lấy records có is_merge=2 từ bảng ward và ___province
- **Xử lý NULL**: Kiểm tra và báo cáo các records không match được

## KẾT QUẢ THỰC HIỆN

### Thống kê tổng quan
- **Tổng số records**: {self.stats.get('total_records', 0):,}
- **Records có ward_pti_id**: {self.stats.get('has_ward_pti_id', 0):,}
- **Records có province_pti_id**: {self.stats.get('has_province_pti_id', 0):,}
- **Records có ward_title**: {self.stats.get('has_ward_title', 0):,}
- **Records có province_title**: {self.stats.get('has_province_title', 0):,}
- **Records hoàn chỉnh**: {self.stats.get('complete_records', 0):,}
- **Tỷ lệ thành công**: {self.stats.get('success_rate', 0)}%

### Phân tích kết quả
- **Thành công**: {self.stats.get('complete_records', 0):,} records
- **Thất bại**: {self.stats.get('total_records', 0) - self.stats.get('complete_records', 0):,} records
- **Tỷ lệ thành công**: {self.stats.get('success_rate', 0)}%

### Thống kê theo tỉnh thành
"""
        
        if 'province_breakdown' in self.stats:
            report_content += "| STT | Mã PTI | Tên tỉnh thành | Số records |\n"
            report_content += "|-----|--------|----------------|------------|\n"
            for i, province in enumerate(self.stats['province_breakdown'][:10], 1):
                report_content += f"| {i} | {province['province_pti_id']} | {province['province_title']} | {province['count']:,} |\n"
            
            if len(self.stats['province_breakdown']) > 10:
                report_content += f"| ... | ... | ... | ... |\n"
                report_content += f"| **Tổng** | | **{len(self.stats['province_breakdown'])} tỉnh thành** | **{sum(p['count'] for p in self.stats['province_breakdown']):,}** |\n"
        
        report_content += f"""
### Vấn đề phát hiện (nếu có)
"""
        
        if 'issues' in self.stats:
            issues = self.stats['issues']
            report_content += f"""- **Tổng records có vấn đề**: {issues['total_issues']:,}
- **Thiếu ward_pti_id**: {issues['missing_ward_pti_id']:,}
- **Thiếu province_pti_id**: {issues['missing_province_pti_id']:,}
- **Thiếu ward_title**: {issues['missing_ward_title']:,}
- **Thiếu province_title**: {issues['missing_province_title']:,}
"""
        
        report_content += f"""
## FILES LIÊN QUAN

### Scripts và SQL
1. **fill_ward_geometry_mapping_data.sql** - Script SQL chính thực hiện fill dữ liệu
2. **run_ward_geometry_mapping.py** - Script Python chạy và tạo báo cáo
3. **update_ward_geometry_ids.sql** - Script cập nhật ward_id và province_id (tham khảo)
4. **mapping_ward_geometry_data.sql** - Script kiểm tra mapping (tham khảo)

### Dữ liệu liên quan
1. **Bảng ward_geometry** - Bảng chính chứa geometry data
2. **Bảng xaphuong** - Bảng chứa dữ liệu xã phường (3,321 records)
3. **Bảng ward** - Bảng ward cũ (với is_merge=2)
4. **Bảng ___province** - Bảng province cũ (với is_merge=2)

### Báo cáo
1. **BAO_CAO_FILL_WARD_GEOMETRY_MAPPING_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md** - Báo cáo này

## KẾT LUẬN

✅ **Hoàn thành thành công** việc fill dữ liệu mapping cho bảng ward_geometry với tỷ lệ thành công {self.stats.get('success_rate', 0)}%.

### Các cột đã được fill:
- ✅ ward_pti_id (từ xaphuong.ma)
- ✅ province_pti_id (từ xaphuong.matinh)  
- ✅ ward_title (từ ward.title)
- ✅ province_title (từ ___province.title)

### Dữ liệu sau khi xử lý:
- **{self.stats.get('complete_records', 0):,} records** có đầy đủ thông tin mapping
- **{len(self.stats.get('province_breakdown', [])):,} tỉnh thành** được xử lý
- **Thời gian xử lý**: {(self.end_time - self.start_time).total_seconds():.2f} giây

---
*Báo cáo được tạo tự động bởi run_ward_geometry_mapping.py*
*Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # Lưu báo cáo
        report_filename = f"BAO_CAO_FILL_WARD_GEOMETRY_MAPPING_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 Báo cáo đã được tạo: {report_filename}")
        return report_filename
    
    def run(self):
        """Chạy toàn bộ quá trình"""
        print("🚀 BẮT ĐẦU FILL DỮ LIỆU MAPPING CHO WARD_GEOMETRY")
        print("=" * 60)
        
        self.start_time = datetime.now()
        
        # Chạy SQL script
        sql_file = "fill_ward_geometry_mapping_data.sql"
        if not os.path.exists(sql_file):
            print(f"❌ Không tìm thấy file SQL: {sql_file}")
            return False
        
        print(f"📝 Chạy SQL script: {sql_file}")
        if not self.run_sql_file(sql_file):
            print("❌ Lỗi chạy SQL script")
            return False
        
        self.end_time = datetime.now()
        
        # Thu thập thống kê
        self.collect_statistics()
        
        # Tạo báo cáo
        report_file = self.generate_report()
        
        # Hiển thị kết quả
        print("\n" + "=" * 60)
        print("📊 KẾT QUẢ TỔNG QUAN:")
        print(f"   • Tổng records: {self.stats.get('total_records', 0):,}")
        print(f"   • Records hoàn chỉnh: {self.stats.get('complete_records', 0):,}")
        print(f"   • Tỷ lệ thành công: {self.stats.get('success_rate', 0)}%")
        print(f"   • Thời gian chạy: {(self.end_time - self.start_time).total_seconds():.2f} giây")
        print(f"   • Báo cáo: {report_file}")
        print("=" * 60)
        print("✅ HOÀN THÀNH!")
        
        return True

if __name__ == "__main__":
    processor = WardGeometryMappingProcessor()
    processor.run()
